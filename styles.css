/*
Generate foundational CSS styles for the entire document and define CSS variables.
Aim for a modern, clean, and elegant aesthetic, similar to Framer's refined designs.
*/

/* 1. CSS Reset / Box Sizing */
* , *::before, *::after {
    box-sizing: border-box;
}
body, h1, h2, h3, p, ul, ol, li {
    margin: 0;
    padding: 0;
}

/* 2. Font Import (Google Fonts) */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@700;900&family=Inter:wght@400;500;700&display=swap');

/* 3. CSS Variables (:root) */
:root {
    /* Refined Colors */
    --primary-bg: #000000;
    --secondary-bg: #0A0A0A;
    --text-color: #AAAAAA;
    --heading-color: #FFFFFF;
    --accent-color-primary: #9933FF;
    --accent-color-secondary: #33CCFF;
    --accent-color-primary-rgb: 153, 51, 255;
    --accent-color-secondary-rgb: 51, 204, 255;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.6);

    /* Typography */
    --font-heading: 'Montserrat', Arial, Helvetica, sans-serif;
    --font-body: 'Inter', Arial, Helvetica, sans-serif;
    --font-size-base: 16px;
    --line-height-base: 1.6;
}

/* 4. Global Body Styles */
body {
    background-color: #000000;
    color: var(--text-color);
    font-family: var(--font-body);
    overflow-x: hidden;
    position: relative;
    min-height: 100vh;
    background-image:
        radial-gradient(circle at center, rgba(var(--accent-color-primary-rgb), 0.05) 0%, transparent 50%),
        url('data:image/svg+xml;charset=utf8,<svg width="10" height="10" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg"><rect width="10" height="10" fill="transparent"/><circle cx="5" cy="5" r="1" fill="%231a1a1a" opacity="0.1"/></svg>');
    background-size: cover, 5px 5px;
    background-repeat: no-repeat, repeat;
    background-position: center center, 0 0;
    background-attachment: fixed;
    background-blend-mode: overlay, normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 5. Heading Styles */
h1, h2, h3 {
    font-family: var(--font-heading);
    color: var(--heading-color);
    line-height: 1.2;
}
h1 {
    font-size: clamp(3rem, 7vw, 5.5rem);
    font-weight: 800;
    letter-spacing: -0.05em;
    line-height: 1.1;
    color: var(--heading-color);
    margin-bottom: 0.5rem;
}
h2 {
    font-size: 2.5rem;
}
h3 {
    font-size: 1.8rem;
}

/* 6. Anchor (<a>) Styles */
a {
    color: var(--accent-color-primary);
    text-decoration: none;
    transition: color 0.2s, text-decoration 0.2s;
}
a:hover, a:focus {
    color: #7a1fff;
    text-decoration: underline;
}

/* 7. Button Base Styles */
button, .cta-btn, .hero-btn {
    background: linear-gradient(135deg, var(--accent-color-primary), var(--accent-color-secondary));
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3), 0 0 20px rgba(153,51,255,0.15) inset;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}
button:hover, .cta-btn:hover, .hero-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.5), 0 0 30px rgba(153,51,255,0.25) inset;
}

/* Hero Section Styles */
#hero-section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, var(--primary-bg) 100%);
    position: relative;
    z-index: 10;
}
#hero-section::before {
    content: '';
    position: absolute;
    top: -20%;
    left: 50%;
    transform: translateX(-50%);
    width: 120vw;
    height: 80vh;
    background: radial-gradient(circle at 50% 0%, rgba(153,51,255,0.35) 0%, rgba(51,204,255,0.10) 60%, transparent 100%);
    z-index: 1;
    pointer-events: none;
}
#hero-section .hero-content {
    position: relative;
    z-index: 2;
}
#hero-section h1 {
    font-size: clamp(3rem, 7vw, 5.5rem);
    font-weight: 800;
    letter-spacing: -0.05em;
    line-height: 1.1;
    color: var(--heading-color);
    margin-bottom: 0.5rem;
}
#hero-section .hero-slogan {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 2rem;
    max-width: 800px;
}
#hero-section .hero-description {
    font-size: clamp(1rem, 1.5vw, 1.1rem);
    color: var(--text-color);
    max-width: 700px;
    margin-bottom: 3rem;
}

/* CTA Section Styles */
#cta-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    padding: 3rem 1rem;
    background: #f8f9fa;
    text-align: center;
}
.cta-block {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
    padding: 2rem 1.5rem;
    max-width: 350px;
    flex: 1 1 300px;
}
.cta-block h3 {
    margin-bottom: 1rem;
    color: #232526;
}
.cta-block p {
    margin-bottom: 1.5rem;
    color: #555;
}
.cta-btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    border-radius: 30px;
    font-weight: bold;
    text-decoration: none;
    color: #fff;
    transition: background 0.3s;
}
.join-btn {
    background: linear-gradient(90deg, var(--accent-color-primary) 0%, var(--accent-color-secondary) 100%);
}
.join-btn:hover {
    background: #218838;
}
.donate-btn {
    background: transparent;
    color: var(--accent-color-primary);
    border: 1.5px solid var(--accent-color-primary);
    box-shadow: none;
}
.donate-btn:hover {
    background: var(--accent-color-primary);
    color: #fff;
    box-shadow: 0 8px 24px var(--shadow-color);
}

/* Main Content Sections Styling */
#about-section, #branches-section, #phases-section, #cta-section {
    padding: 100px 5% 100px 5%;
    text-align: center;
}
.section-content {
    max-width: 1200px;
    margin: 0 auto;
    display: block;
}
#branches-section .section-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    padding-bottom: 80px;
}
#phases-section .section-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}
@media (min-width: 900px) {
    #phases-section .section-content {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}
#cta-section .section-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}
@media (min-width: 700px) {
    #cta-section .section-content {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}
#about-section, #phases-section {
    background-color: var(--secondary-bg);
}
#branches-section, #cta-section {
    background-color: var(--primary-bg);
    border-bottom: 1px solid var(--border-color);
}
#about-section h2, #branches-section h2, #phases-section h2, #cta-section h2 {
    margin-bottom: 60px;
    font-size: 2.8rem;
    letter-spacing: 0.03em;
}
#about-section p, #branches-section p, #phases-section p, #cta-section p {
    max-width: 800px;
    margin: 0 auto 20px auto;
    line-height: 1.7;
    font-size: 1.15rem;
}
#cta-section .cta-btn {
    background-color: var(--button-primary-bg);
    color: var(--primary-bg);
    margin: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}
#cta-section .cta-btn:hover {
    box-shadow: 0 8px 16px rgba(0,0,0,0.30);
    background-color: var(--accent-color);
    color: var(--heading-color);
}

/* 1. Card Component */
.card {
    background-color: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2), 0 0 40px rgba(153,51,255,0.05) inset;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4), 0 0 60px rgba(153,51,255,0.1) inset;
}

/* 3. Card Content Alignment */
.card {
    text-align: left;
}
.card h3 {
    margin-top: 0;
    margin-bottom: 20px;
}
.card p, .card ol, .card ul {
    margin-bottom: 18px;
}

/* 4. Custom List Styling in Cards */
.card ul, .card ol {
    list-style: none;
    padding-left: 0;
}
.card ul li, .card ol li {
    position: relative;
    padding-left: 1.5em;
    margin-bottom: 12px;
}
.card ul li::before, .card ol li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.7em;
    width: 0.7em;
    height: 0.7em;
    background: var(--accent-color-primary);
    border-radius: 50%;
    box-shadow: 0 0 6px var(--accent-color-secondary);
}

/* Responsive adjustments for sections */
@media (max-width: 800px) {
    #about-section, #branches-section, #phases-section, #cta-section {
        padding: 60px 2% 60px 2%;
    }
    .section-content {
        max-width: 98vw;
    }
    #about-section h2, #branches-section h2, #phases-section h2, #cta-section h2 {
        font-size: 2rem;
    }
}
