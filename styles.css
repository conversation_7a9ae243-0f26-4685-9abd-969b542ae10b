/*
Apply exact CSS values extracted from xtract.framer.ai for global styles,
Hero section, and core components (cards and buttons).
This includes specific colors, gradients, shadows, and typography details.
*/

/* 1. CSS Reset / Box Sizing */
* , *::before, *::after {
    box-sizing: border-box;
}
body, h1, h2, h3, p, ul, ol, li {
    margin: 0;
    padding: 0;
}

/* 2. Font Import (Google Fonts + Figtree) */
@import url('https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700&family=Inter:wght@400;500;700&display=swap');

/* 3. Exact Color Variables (:root section - OVERWRITE existing variables) */
:root {
    /* Exact Xtract Color Tokens */
    --token-aae92d1e-1be4-447e-8038-565c6944f982: rgb(0, 0, 0); /* Pure Black - Primary Background */
    --token-a888adc2-3d05-4d6f-88b7-8dab97418795: rgb(129, 74, 200); /* Purple - Primary Accent */
    --token-cd9ad879-0340-4881-8da6-7efa110062d4: rgb(223, 122, 254); /* Pink - Secondary Accent / Gradient End */
    --token-b2fb23d9-6070-4f2d-b3d0-2fe68c4f2aab: rgba(13, 13, 13, .8); /* Dark Transparent Grey - Card Background */
    --token-55fce8bf-ab86-42dc-8b77-6335cf9cf588: rgb(255, 255, 255); /* White - Heading/Bright Text */
    --token-cfb0af42-62a5-486a-837b-9870b5e7030b: rgba(255, 255, 255, .05); /* Very Light Transparent White - Subtle Borders/Highlights */
    --token-d072d1f5-ef86-4b7c-bae1-6c9f6238e10b: rgb(204, 204, 204); /* Light Grey - Body Text */
    --token-313dd4d6-9859-4bdd-889b-954a849d13e3: rgb(34, 34, 34); /* Dark Grey - Border Color */
    --token-5ca39c84-aa81-4600-807d-fc1e6e718e60: rgba(255, 255, 255, .9); /* Slightly Transparent White for some texts */
    --token-73ebc18b-e0a6-4828-83ca-5395b5fe5857: rgba(255, 255, 255, .75); /* More Transparent White for some texts */
    --framer-link-text-color: rgb(0, 153, 255); /* Blue for links */
    --framer-link-text-decoration: underline;
    --framer-paragraph-spacing: 0px;
    --framer-text-wrap-override: none;

    /* Define RGB versions for gradients with opacity */
    --accent-color-primary-rgb: 129, 74, 200;
    --accent-color-secondary-rgb: 223, 122, 254;

    /* Legacy variables for backward compatibility */
    --primary-bg: var(--token-aae92d1e-1be4-447e-8038-565c6944f982);
    --text-color: var(--token-d072d1f5-ef86-4b7c-bae1-6c9f6238e10b);
    --heading-color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    --accent-color-primary: var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    --accent-color-secondary: var(--token-cd9ad879-0340-4881-8da6-7efa110062d4);
}

/* 4. Global Body Styles (OVERWRITE previous body styles) */
body {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    background-color: var(--token-aae92d1e-1be4-447e-8038-565c6944f982);
    color: var(--token-d072d1f5-ef86-4b7c-bae1-6c9f6238e10b);
    font-family: Figtree, "Figtree Placeholder", sans-serif;
    font-size: 16px;
    line-height: 1.5em;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Implement the subtle radial glow and noise texture using ::before pseudo-element for body */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-image:
        radial-gradient(circle at center, rgba(var(--accent-color-primary-rgb), 0.03) 0%, transparent 50%),
        url('data:image/svg+xml;charset=utf8,<svg width="10" height="10" viewBox="0 0 10 10" xmlns="http://www.w3.org/2000/svg"><rect width="10" height="10" fill="transparent"/><circle cx="5" cy="5" r="1" fill="%231a1a1a" opacity="0.05"/></svg>');
    background-size: cover, 10px 10px;
    background-repeat: no-repeat, repeat;
    background-blend-mode: overlay;
    opacity: 0.4;
    z-index: -1;
    pointer-events: none;
}

/* 5. Heading Styles */
h1, h2, h3 {
    font-family: "Figtree", sans-serif;
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    line-height: 1.2;
}
h1 {
    font-family: "Figtree", sans-serif;
    font-size: clamp(45px, 7vw, 70px);
    font-weight: 600;
    letter-spacing: -2.2px;
    line-height: 1.1em;
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    margin-bottom: 1rem;
}
h2 {
    font-family: "Figtree Variable", sans-serif;
    font-size: clamp(28px, 4vw, 50px);
    font-weight: 550;
    letter-spacing: -0.04em;
    line-height: 1.1em;
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    text-align: center;
    margin-bottom: 60px;
}
h3 {
    font-size: 1.8rem;
}

/* 6. Anchor (<a>) Styles */
a {
    color: var(--framer-link-text-color);
    text-decoration: var(--framer-link-text-decoration);
    transition: color 0.2s, text-decoration 0.2s;
}
a:hover, a:focus {
    color: var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    text-decoration: underline;
}

/* 7. Button Base Styles - Hero Button with Exact Xtract Values */
.hero-btn {
    background-color: var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    border-radius: 6px;
    padding: 9px 13px;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0.706592px 0.706592px -0.625px, rgba(0, 0, 0, 0.145) 0px 1.80656px 1.80656px -1.25px, rgba(0, 0, 0, 0.137) 0px 3.62176px 3.62176px -1.875px, rgba(0, 0, 0, 0.125) 0px 6.8656px 6.8656px -2.5px, rgba(0, 0, 0, 0.106) 0px 13.6468px 13.6468px -3.125px, rgba(0, 0, 0, 0.05) 0px 30px 30px -3.75px;
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    text-decoration: none;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}
.hero-btn:hover {
    transform: translateY(-2px);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 1px -0.625px, rgba(0, 0, 0, 0.18) 0px 2.5px 2.5px -1.25px, rgba(0, 0, 0, 0.15) 0px 5px 5px -1.875px, rgba(0, 0, 0, 0.13) 0px 9px 9px -2.5px, rgba(0, 0, 0, 0.11) 0px 18px 18px -3.125px, rgba(0, 0, 0, 0.07) 0px 40px 40px -3.75px, 0 0 20px rgba(var(--accent-color-primary-rgb), 0.3);
}

/* Hero Section Styles (OVERWRITE previous styles) */
#hero-section {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    background: linear-gradient(180deg, rgba(0,0,0,0) 0%, var(--token-aae92d1e-1be4-447e-8038-565c6944f982) 100%);
}
#hero-section::before {
    content: '';
    position: absolute;
    top: -20%;
    left: 50%;
    transform: translateX(-50%);
    width: 120vw;
    height: 80vh;
    background: radial-gradient(circle at 50% 0%, rgba(var(--accent-color-primary-rgb), 0.35) 0%, rgba(var(--accent-color-secondary-rgb), 0.10) 60%, transparent 100%);
    z-index: 1;
    pointer-events: none;
}
#hero-section .hero-content {
    position: relative;
    z-index: 2;
}
#hero-section h1 {
    font-family: "Figtree", sans-serif;
    font-size: clamp(45px, 7vw, 70px);
    font-weight: 600;
    letter-spacing: -2.2px;
    line-height: 1.1em;
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    margin-bottom: 1rem;
}
#hero-section .hero-slogan {
    font-family: "Figtree", sans-serif;
    font-size: clamp(16px, 2.5vw, 18px);
    font-weight: 500;
    color: var(--token-d072d1f5-ef86-4b7c-bae1-6c9f6238e10b);
    max-width: 600px;
    margin: 0 auto 2rem auto;
}
#hero-section .hero-description {
    font-size: clamp(1rem, 1.5vw, 1.1rem);
    color: var(--token-d072d1f5-ef86-4b7c-bae1-6c9f6238e10b);
    max-width: 700px;
    margin-bottom: 3rem;
}

/* CTA Section Styles */
#cta-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    padding: 3rem 1rem;
    background: #f8f9fa;
    text-align: center;
}
.cta-block {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
    padding: 2rem 1.5rem;
    max-width: 350px;
    flex: 1 1 300px;
}
.cta-block h3 {
    margin-bottom: 1rem;
    color: #232526;
}
.cta-block p {
    margin-bottom: 1.5rem;
    color: #555;
}
.cta-btn {
    display: inline-block;
    padding: 0.75rem 2rem;
    border-radius: 30px;
    font-weight: bold;
    text-decoration: none;
    color: #fff;
    transition: background 0.3s;
}
/* Refine CTA Buttons in #cta-section */
.join-btn {
    background-color: var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    border-radius: 6px;
    padding: 9px 13px;
    box-shadow: rgba(0, 0, 0, 0.15) 0px 0.706592px 0.706592px -0.625px, rgba(0, 0, 0, 0.145) 0px 1.80656px 1.80656px -1.25px, rgba(0, 0, 0, 0.137) 0px 3.62176px 3.62176px -1.875px, rgba(0, 0, 0, 0.125) 0px 6.8656px 6.8656px -2.5px, rgba(0, 0, 0, 0.106) 0px 13.6468px 13.6468px -3.125px, rgba(0, 0, 0, 0.05) 0px 30px 30px -3.75px;
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    border: none;
}
.join-btn:hover {
    transform: translateY(-2px);
    box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 1px -0.625px, rgba(0, 0, 0, 0.18) 0px 2.5px 2.5px -1.25px, rgba(0, 0, 0, 0.15) 0px 5px 5px -1.875px, rgba(0, 0, 0, 0.13) 0px 9px 9px -2.5px, rgba(0, 0, 0, 0.11) 0px 18px 18px -3.125px, rgba(0, 0, 0, 0.07) 0px 40px 40px -3.75px;
}
.donate-btn {
    background-color: transparent;
    border: 1px solid var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    color: var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    border-radius: 6px;
    padding: 9px 13px;
    box-shadow: none;
}
.donate-btn:hover {
    background-color: var(--token-a888adc2-3d05-4d6f-88b7-8dab97418795);
    color: var(--token-55fce8bf-ab86-42dc-8b77-6335cf9cf588);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

/* Main Content Sections Styling - Refine General Section Styling */
#about-section, #branches-section, #phases-section, #cta-section {
    padding: 100px 5% 100px 5%;
}
.section-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 100px 40px;
    display: block;
}
#branches-section .section-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    padding-bottom: 80px;
}
#phases-section .section-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}
@media (min-width: 900px) {
    #phases-section .section-content {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
}
#cta-section .section-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 40px;
}
@media (min-width: 700px) {
    #cta-section .section-content {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}
#about-section, #phases-section {
    background-color: var(--secondary-bg);
}
#branches-section, #cta-section {
    background-color: var(--primary-bg);
    border-bottom: 1px solid var(--border-color);
}
#about-section h2, #branches-section h2, #phases-section h2, #cta-section h2 {
    margin-bottom: 60px;
    font-size: 2.8rem;
    letter-spacing: 0.03em;
}
#about-section p, #branches-section p, #phases-section p, #cta-section p {
    max-width: 800px;
    margin: 0 auto 20px auto;
    line-height: 1.7;
    font-size: 1.15rem;
}
#cta-section .cta-btn {
    background-color: var(--button-primary-bg);
    color: var(--primary-bg);
    margin: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}
#cta-section .cta-btn:hover {
    box-shadow: 0 8px 16px rgba(0,0,0,0.30);
    background-color: var(--accent-color);
    color: var(--heading-color);
}

/* Card Component - Refine Card Component (.card) Styling (OVERWRITE previous .card styles) */
.card {
    background-color: var(--token-b2fb23d9-6070-4f2d-b3d0-2fe68c4f2aab);
    border: 1px solid var(--token-313dd4d6-9859-4bdd-889b-954a849d13e3);
    border-radius: 12px;
    padding: 50px;
    box-shadow: rgba(0, 0, 0, 0.26) 0px 0.636953px 1.14652px -1.125px, rgba(0, 0, 0, 0.24) 0px 1.9316px 3.47689px -2.25px, rgba(0, 0, 0, 0.192) 0px 5.10612px 9.19102px -3.375px, rgba(0, 0, 0, 0.03) 0px 16px 28.8px -4.5px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease-in-out;
    text-align: left;
}
.card:hover {
    transform: translateY(-5px);
    box-shadow: rgba(0, 0, 0, 0.35) 0px 1px 2px -1.125px, rgba(0, 0, 0, 0.32) 0px 3px 5px -2.25px, rgba(0, 0, 0, 0.25) 0px 7px 12px -3.375px, rgba(0, 0, 0, 0.05) 0px 22px 38px -4.5px, 0 0 25px rgba(var(--accent-color-primary-rgb), 0.15);
}

/* 3. Card Content Alignment */
.card {
    text-align: left;
}
.card h3 {
    margin-top: 0;
    margin-bottom: 20px;
}
.card p, .card ol, .card ul {
    margin-bottom: 18px;
}

/* 4. Custom List Styling in Cards */
.card ul, .card ol {
    list-style: none;
    padding-left: 0;
}
.card ul li, .card ol li {
    position: relative;
    padding-left: 1.5em;
    margin-bottom: 12px;
}
.card ul li::before, .card ol li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0.7em;
    width: 0.7em;
    height: 0.7em;
    background: var(--accent-color-primary);
    border-radius: 50%;
    box-shadow: 0 0 6px var(--accent-color-secondary);
}

/* Responsive adjustments for sections */
@media (max-width: 800px) {
    #about-section, #branches-section, #phases-section, #cta-section {
        padding: 60px 2% 60px 2%;
    }
    .section-content {
        max-width: 98vw;
    }
    #about-section h2, #branches-section h2, #phases-section h2, #cta-section h2 {
        font-size: 2rem;
    }
}
